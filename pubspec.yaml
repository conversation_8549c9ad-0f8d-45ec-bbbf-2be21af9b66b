name: sixam_mart
description: A Multi-vendor1 Food. Grocery, eCommerce, Pharmacy & Parcel service.
publish_to: 'none' #
version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  get: ^4.6.6
  shared_preferences: ^2.3.4
  connectivity_plus: ^6.1.3
  firebase_core: ^3.4.0
  firebase_messaging: ^15.1.0
  flutter_local_notifications: ^17.2.2
  firebase_crashlytics: ^4.1.3
  firebase_auth: ^5.2.0
  path_provider: ^2.1.4
  url_strategy: ^0.3.0
  pin_code_fields: ^8.0.1
  geolocator: ^13.0.1
  google_maps_flutter: ^2.9.0
  google_maps_flutter_web: ^0.5.10
  flutter_typeahead: ^4.3.7
  carousel_slider: ^5.0.0
  shimmer_animation: ^2.2.1
  image_picker: ^1.1.2
  image_picker_ios: ^0.8.5+6
  universal_html: ^2.2.4
  flutter_inappwebview: ^6.1.3
  url_launcher: ^6.3.0
  http: ^1.2.2
  pointer_interceptor: ^0.10.1+2
  phone_numbers_parser: ^9.0.0
  country_code_picker: ^3.0.0
  cached_network_image: ^3.4.1
  photo_view: ^0.15.0
#  image_compression_flutter: ^1.0.4
  dotted_border: ^2.1.0
  share_plus: ^10.0.2
  expandable_bottom_sheet: ^1.1.1+1
  google_sign_in: ^6.2.1
  google_sign_in_web: ^0.12.4
  flutter_facebook_auth: ^6.0.4
  flutter_animarker: ^3.2.0
  sign_in_with_apple: ^6.1.3
  intl: ^0.19.0
  just_the_tooltip: ^0.0.12
  flutter_slidable: ^3.1.2
  animated_flip_counter: ^0.3.4
  youtube_player_iframe: ^5.1.5
  card_swiper: ^3.0.1
  audioplayers: ^6.1.0
  video_player: ^2.10.0
  flutter_svg: ^2.0.10+1
  flutter_widget_from_html_core: ^0.15.2
  image: ^4.2.0
  smooth_page_indicator: ^1.2.0+3
  chewie: ^1.8.4
  location: ^7.0.0
  drift: ^2.21.0
  drift_flutter: ^0.2.1
  syncfusion_flutter_datepicker: ^27.2.3
  flutter_swipe_button: ^2.1.3
  lottie: ^3.2.0
  dropdown_button2: ^2.3.9
  custom_info_window: ^1.0.1
  image_network: ^2.6.0


dependency_overrides:
  collection: ^1.19.1
  web: ^1.1.0
  webview_flutter_android: ^4.8.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0
  drift_dev: ^2.21.2
  build_runner: ^2.4.13

flutter:
  uses-material-design: true

  assets:
    - assets/image/
    - assets/language/
    - assets/map/
    - assets/json/

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/font/Roboto-Regular.ttf
          weight: 400
        - asset: assets/font/Roboto-Medium.ttf
          weight: 500
        - asset: assets/font/Roboto-Bold.ttf
          weight: 700
        - asset: assets/font/Roboto-Black.ttf
          weight: 900