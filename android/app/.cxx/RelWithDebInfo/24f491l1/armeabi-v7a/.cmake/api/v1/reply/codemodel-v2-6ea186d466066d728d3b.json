{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Projects/6amMart-User-App/android/app/.cxx/RelWithDebInfo/24f491l1/armeabi-v7a", "source": "/Users/<USER>/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}