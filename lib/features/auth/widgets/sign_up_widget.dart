import 'dart:async';
import 'dart:convert';

import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sixam_mart/common/models/response_model.dart';
import 'package:sixam_mart/common/widgets/custom_button.dart';
import 'package:sixam_mart/common/widgets/custom_snackbar.dart';
import 'package:sixam_mart/common/widgets/custom_text_field.dart';
import 'package:sixam_mart/features/auth/controllers/auth_controller.dart';
import 'package:sixam_mart/features/auth/domain/enum/centralize_login_enum.dart';
import 'package:sixam_mart/features/auth/domain/models/signup_body_model.dart';
import 'package:sixam_mart/features/auth/widgets/auth_dialog_widget.dart';
import 'package:sixam_mart/features/auth/widgets/condition_check_box_widget.dart';
import 'package:sixam_mart/features/cart/controllers/cart_controller.dart';
import 'package:sixam_mart/features/language/controllers/language_controller.dart';
import 'package:sixam_mart/features/location/controllers/location_controller.dart';
import 'package:sixam_mart/features/profile/controllers/profile_controller.dart';
import 'package:sixam_mart/features/splash/controllers/splash_controller.dart';
import 'package:sixam_mart/features/verification/screens/verification_screen.dart';
import 'package:sixam_mart/helper/responsive_helper.dart';
import 'package:sixam_mart/helper/route_helper.dart';
import 'package:sixam_mart/helper/validate_check.dart';
import 'package:sixam_mart/util/dimensions.dart';
import 'package:sixam_mart/util/images.dart';
import 'package:sixam_mart/util/styles.dart';

import '../domain/models/phone_check_model.dart';

class SignUpWidget extends StatefulWidget {
  const SignUpWidget({super.key});

  @override
  SignUpWidgetState createState() => SignUpWidgetState();
}

class SignUpWidgetState extends State<SignUpWidget> {
  final FocusNode _nameFocus = FocusNode();
  final FocusNode _emailFocus = FocusNode();
  final FocusNode _phoneFocus = FocusNode();
  final FocusNode _passwordFocus = FocusNode();
  final FocusNode _confirmPasswordFocus = FocusNode();
  final FocusNode _referCodeFocus = FocusNode();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final TextEditingController _referCodeController = TextEditingController();
  String? _countryDialCode;
  GlobalKey<FormState>? _formKeySignUp;

  // Phone check related variables
  bool _isPhoneFieldsDisabled = false;
  String? _phoneCheckMessage;
  bool _isPhoneCheckSuccess = false;
  Timer? _phoneCheckTimer;

  @override
  void initState() {
    super.initState();
    _formKeySignUp = GlobalKey<FormState>();
    _countryDialCode = '';
    // CountryCode.fromCountryCode(
    //     Get.find<SplashController>().configModel!.country!)
    // .dialCode;
  }

  @override
  void dispose() {
    _phoneCheckTimer?.cancel();
    super.dispose();
  }

  void _onPhoneChanged(String value) {
    // Cancel previous timer
    _phoneCheckTimer?.cancel();

    // Clear previous results
    setState(() {
      _isPhoneFieldsDisabled = false;
      _phoneCheckMessage = null;
      _isPhoneCheckSuccess = false;
    });

    // Clear phone check result in controller
    Get.find<AuthController>().clearPhoneCheckResult();

    // Only start phone check if phone number is exactly 10 digits
    if (value.isNotEmpty && value.length == 10) {
      _phoneCheckTimer = Timer(const Duration(milliseconds: 800), () {
        _checkPhone(value);
      });
    } else if (value.isNotEmpty && value.length < 10) {
      // Show validation message for incomplete phone number
      setState(() {
        _phoneCheckMessage = 'phone_number_must_be_10_digits'.tr;
        _isPhoneCheckSuccess = false;
      });
    }
  }

  Future<void> _checkPhone(String phoneNumber) async {
    String fullPhoneNumber = phoneNumber;

    PhoneCheckModel? result =
        await Get.find<AuthController>().checkPhone(fullPhoneNumber);

    if (result != null) {
      setState(() {
        if (result.isPhoneAvailable) {
          // Case 1: Phone doesn't exist - continue normal flow
          _isPhoneFieldsDisabled = false;
          _phoneCheckMessage = null;
          _isPhoneCheckSuccess = false;
        } else if (result.isPhoneExistsInBothSystems) {
          // Case 2: Phone exists in both systems - show error
          _isPhoneFieldsDisabled = false;
          _phoneCheckMessage = 'your_phone_number_already_exists'.tr;
          _isPhoneCheckSuccess = false;
        } else if (result.isPhoneExistsInExternalSystemOnly) {
          // Case 3: Phone exists in external system only
          _isPhoneFieldsDisabled = true;
          _phoneCheckMessage = 'your_phone_number_already_registered_with'
              .tr
              .replaceAll('{name}', result.customerNameFromExternal ?? '');
          _isPhoneCheckSuccess = true;

          // Set the username from external data
          if (result.customerNameFromExternal != null) {
            _nameController.text = result.customerNameFromExternal!;
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isDesktop = ResponsiveHelper.isDesktop(context);
    return Form(
      key: _formKeySignUp,
      child: Container(
        width: context.width > 700 ? 700 : context.width,
        decoration: context.width > 700
            ? BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(Dimensions.radiusSmall),
              )
            : null,
        padding: EdgeInsets.symmetric(
            horizontal: isDesktop ? Dimensions.paddingSizeDefault : 0),
        child: GetBuilder<AuthController>(builder: (authController) {
          return Column(mainAxisSize: MainAxisSize.min, children: [
            isDesktop
                ? Align(
                    alignment: Alignment.topRight,
                    child: IconButton(
                        onPressed: () => Get.back(),
                        icon: const Icon(Icons.clear)),
                  )
                : const SizedBox(),
            Padding(
              padding: EdgeInsets.all(
                  isDesktop ? Dimensions.paddingSizeExtraLarge : 0),
              child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    isDesktop
                        ? Padding(
                            padding: const EdgeInsets.symmetric(
                                vertical: Dimensions.paddingSizeLarge),
                            child: Image.asset(Images.logo, width: 125),
                          )
                        : const SizedBox(),
                    isDesktop
                        ? Align(
                            alignment: Alignment.topLeft,
                            child: Text('sign_up'.tr,
                                style: robotoBold.copyWith(
                                    fontSize: Dimensions.fontSizeExtraLarge)),
                          )
                        : const SizedBox(),
                    SizedBox(
                        height: isDesktop
                            ? Dimensions.paddingSizeExtraLarge
                            : Dimensions.paddingSizeSmall),
                    Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: CustomTextField(
                              titleText: 'ex_jhon'.tr,
                              labelText: 'user_name'.tr,
                              showLabelText: true,
                              required: true,
                              controller: _nameController,
                              focusNode: _nameFocus,
                              nextFocus:
                                  isDesktop ? _referCodeFocus : _phoneFocus,
                              inputType: TextInputType.name,
                              capitalization: TextCapitalization.words,
                              isEnabled: !_isPhoneFieldsDisabled,
                              prefixIcon: CupertinoIcons.person_alt_circle_fill,
                              validator: (value) =>
                                  ValidateCheck.validateEmptyText(
                                      value, "please_enter_your_name".tr),
                            ),
                          ),
                          SizedBox(
                              width: Get.find<SplashController>()
                                              .configModel!
                                              .refEarningStatus ==
                                          1 &&
                                      isDesktop
                                  ? Dimensions.paddingSizeSmall
                                  : 0),
                          (Get.find<SplashController>()
                                          .configModel!
                                          .refEarningStatus ==
                                      1 &&
                                  isDesktop)
                              ? Expanded(
                                  child: CustomTextField(
                                    titleText: 'refer_code'.tr,
                                    labelText: 'refer_code'.tr,
                                    showLabelText: true,
                                    controller: _referCodeController,
                                    focusNode: _referCodeFocus,
                                    nextFocus:
                                        isDesktop ? _emailFocus : _phoneFocus,
                                    inputType: TextInputType.text,
                                    capitalization: TextCapitalization.words,
                                    prefixImage: Images.referCode,
                                    divider: false,
                                    prefixSize: 14,
                                  ),
                                )
                              : const SizedBox(),
                        ]),
                    SizedBox(
                        height: isDesktop
                            ? Dimensions.paddingSizeExtraLarge
                            : Dimensions.paddingSizeLarge),
                    Row(children: [
                      // isDesktop
                      //     ? Expanded(
                      //         child: CustomTextField(
                      //           titleText: 'enter_email'.tr,
                      //           labelText: 'email'.tr,
                      //           showLabelText: true,
                      //           required: true,
                      //           controller: _emailController,
                      //           focusNode: _emailFocus,
                      //           nextFocus:
                      //               isDesktop ? _phoneFocus : _passwordFocus,
                      //           inputType: TextInputType.emailAddress,
                      //           prefixIcon: CupertinoIcons.mail_solid,
                      //           validator: (value) =>
                      //               ValidateCheck.validateEmail(value),
                      //         ),
                      //       )
                      //     : const SizedBox(),
                      // SizedBox(
                      //     width: isDesktop ? Dimensions.paddingSizeSmall : 0),
                      Expanded(
                        child: CustomTextField(
                          titleText: 'xxx-xxx-xxxxx'.tr,
                          labelText: 'phone'.tr,
                          showLabelText: true,
                          required: true,
                          controller: _phoneController,
                          focusNode: _phoneFocus,
                          nextFocus: isDesktop ? _passwordFocus : _emailFocus,
                          inputType: TextInputType.phone,
                          isPhone: true,
                          isEnabled: !_isPhoneFieldsDisabled,
                          prefixIcon: CupertinoIcons.phone_fill,
                          suffixChild:
                              Get.find<AuthController>().isPhoneChecking
                                  ? const Padding(
                                      padding: EdgeInsets.all(12.0),
                                      child: SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                            strokeWidth: 2),
                                      ),
                                    )
                                  : null,
                          onChanged: _onPhoneChanged,
                          onCountryChanged: (CountryCode countryCode) {
                            _countryDialCode = countryCode.dialCode;
                            // Trigger phone check when country code changes
                            if (_phoneController.text.isNotEmpty) {
                              _onPhoneChanged(_phoneController.text);
                            }
                          },
                          countryDialCode: _countryDialCode != null
                              ? CountryCode.fromCountryCode(
                                      Get.find<SplashController>()
                                          .configModel!
                                          .country!)
                                  .code
                              : Get.find<LocalizationController>()
                                  .locale
                                  .countryCode,
                          validator: (value) => ValidateCheck.validateEmptyText(
                              value, "please_enter_phone_number".tr),
                        ),
                      ),
                    ]),

                    // Phone check message display
                    if (_phoneCheckMessage != null) ...[
                      const SizedBox(height: Dimensions.paddingSizeSmall),
                      Container(
                        width: double.infinity,
                        padding:
                            const EdgeInsets.all(Dimensions.paddingSizeSmall),
                        decoration: BoxDecoration(
                          color: _isPhoneCheckSuccess
                              ? Colors.green.withValues(alpha: 0.1)
                              : Colors.red.withValues(alpha: 0.1),
                          borderRadius:
                              BorderRadius.circular(Dimensions.radiusSmall),
                          border: Border.all(
                            color: _isPhoneCheckSuccess
                                ? Colors.green
                                : Colors.red,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              _isPhoneCheckSuccess
                                  ? Icons.check_circle
                                  : Icons.error,
                              color: _isPhoneCheckSuccess
                                  ? Colors.green
                                  : Colors.red,
                              size: 20,
                            ),
                            const SizedBox(width: Dimensions.paddingSizeSmall),
                            Expanded(
                              child: Text(
                                _phoneCheckMessage!,
                                style: robotoRegular.copyWith(
                                  color: _isPhoneCheckSuccess
                                      ? Colors.green
                                      : Colors.red,
                                  fontSize: Dimensions.fontSizeSmall,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],

                    SizedBox(
                        height: isDesktop
                            ? Dimensions.paddingSizeExtraLarge
                            : Dimensions.paddingSizeLarge),
                    // !isDesktop
                    //     ? CustomTextField(
                    //         labelText: 'email'.tr,
                    //         titleText: 'enter_email'.tr,
                    //         showLabelText: true,
                    //         required: true,
                    //         controller: _emailController,
                    //         focusNode: _emailFocus,
                    //         nextFocus: _passwordFocus,
                    //         inputType: TextInputType.emailAddress,
                    //         prefixIcon: CupertinoIcons.mail_solid,
                    //         validator: (value) =>
                    //             ValidateCheck.validateEmail(value),
                    //         divider: false,
                    //       )
                    //     : const SizedBox(),
                    // SizedBox(
                    //     height: !isDesktop ? Dimensions.paddingSizeLarge : 0),
                    Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(children: [
                              CustomTextField(
                                titleText: '8+characters'.tr,
                                labelText: 'password'.tr,
                                showLabelText: true,
                                required: true,
                                controller: _passwordController,
                                focusNode: _passwordFocus,
                                nextFocus: _confirmPasswordFocus,
                                inputType: TextInputType.visiblePassword,
                                prefixIcon: Icons.lock,
                                isPassword: true,
                                validator: (value) =>
                                    ValidateCheck.validateEmptyText(
                                        value, "please_enter_password".tr),
                              ),
                            ]),
                          ),
                          SizedBox(
                              width:
                                  isDesktop ? Dimensions.paddingSizeSmall : 0),
                          isDesktop
                              ? Expanded(
                                  child: CustomTextField(
                                  titleText: '8+characters'.tr,
                                  labelText: 'confirm_password'.tr,
                                  showLabelText: true,
                                  required: true,
                                  controller: _confirmPasswordController,
                                  focusNode: _confirmPasswordFocus,
                                  nextFocus: Get.find<SplashController>()
                                              .configModel!
                                              .refEarningStatus ==
                                          1
                                      ? _referCodeFocus
                                      : null,
                                  inputAction: Get.find<SplashController>()
                                              .configModel!
                                              .refEarningStatus ==
                                          1
                                      ? TextInputAction.next
                                      : TextInputAction.done,
                                  inputType: TextInputType.visiblePassword,
                                  prefixIcon: Icons.lock,
                                  isPassword: true,
                                  onSubmit: (text) => (GetPlatform.isWeb)
                                      ? _register(authController, '')
                                      : null,
                                  validator: (value) =>
                                      ValidateCheck.validateConfirmPassword(
                                          value, _passwordController.text),
                                ))
                              : const SizedBox()
                        ]),
                    SizedBox(
                        height: isDesktop
                            ? Dimensions.paddingSizeExtraLarge
                            : Dimensions.paddingSizeLarge),
                    !isDesktop
                        ? CustomTextField(
                            titleText: '8+characters'.tr,
                            labelText: 'confirm_password'.tr,
                            showLabelText: true,
                            required: true,
                            controller: _confirmPasswordController,
                            focusNode: _confirmPasswordFocus,
                            nextFocus: Get.find<SplashController>()
                                        .configModel!
                                        .refEarningStatus ==
                                    1
                                ? _referCodeFocus
                                : null,
                            inputAction: Get.find<SplashController>()
                                        .configModel!
                                        .refEarningStatus ==
                                    1
                                ? TextInputAction.next
                                : TextInputAction.done,
                            inputType: TextInputType.visiblePassword,
                            prefixIcon: Icons.lock,
                            isPassword: true,
                            onSubmit: (text) => (GetPlatform.isWeb)
                                ? _register(authController, _countryDialCode!)
                                : null,
                            validator: (value) =>
                                ValidateCheck.validateConfirmPassword(
                                    value, _passwordController.text),
                          )
                        : const SizedBox(),
                    SizedBox(
                        height: !isDesktop ? Dimensions.paddingSizeLarge : 0),
                    (Get.find<SplashController>()
                                    .configModel!
                                    .refEarningStatus ==
                                1 &&
                            !isDesktop)
                        ? CustomTextField(
                            titleText: 'refer_code'.tr,
                            labelText: 'refer_code'.tr,
                            showLabelText: true,
                            controller: _referCodeController,
                            focusNode: _referCodeFocus,
                            inputAction: TextInputAction.done,
                            inputType: TextInputType.text,
                            capitalization: TextCapitalization.words,
                            prefixImage: Images.referCode,
                            divider: false,
                            prefixSize: 14,
                          )
                        : const SizedBox(),
                    SizedBox(
                        height: isDesktop ? 0 : Dimensions.paddingSizeLarge),
                    const ConditionCheckBoxWidget(forDeliveryMan: true),
                    SizedBox(
                        height: isDesktop
                            ? Dimensions.paddingSizeExtraLarge
                            : Dimensions.paddingSizeDefault),
                    CustomButton(
                      height: isDesktop ? 50 : null,
                      width: isDesktop ? 250 : null,
                      radius: isDesktop
                          ? Dimensions.radiusSmall
                          : Dimensions.radiusDefault,
                      isBold: !isDesktop,
                      fontSize: isDesktop ? Dimensions.fontSizeSmall : null,
                      buttonText: 'sign_up'.tr,
                      isLoading: authController.isLoading ||
                          authController.isPhoneChecking,
                      onPressed: authController.acceptTerms &&
                              !authController.isPhoneChecking
                          ? () => _register(authController, _countryDialCode!)
                          : null,
                    ),
                    SizedBox(
                        height: isDesktop
                            ? Dimensions.paddingSizeExtraLarge
                            : Dimensions.paddingSizeDefault),
                    Padding(
                      padding: const EdgeInsets.only(
                          bottom: Dimensions.paddingSizeLarge),
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text('already_have_account'.tr,
                                style: robotoRegular.copyWith(
                                    color: Theme.of(context).hintColor)),
                            InkWell(
                              onTap: authController.isLoading
                                  ? null
                                  : () {
                                      if (isDesktop) {
                                        Get.back();
                                        Get.dialog(const Center(
                                            child: AuthDialogWidget(
                                                exitFromApp: false,
                                                backFromThis: false)));
                                      } else {
                                        if (Get.currentRoute ==
                                            RouteHelper.signUp) {
                                          Get.back();
                                        } else {
                                          Get.toNamed(
                                              RouteHelper.getSignInRoute(
                                                  RouteHelper.signUp));
                                        }
                                      }
                                    },
                              child: Padding(
                                padding: const EdgeInsets.all(
                                    Dimensions.paddingSizeExtraSmall),
                                child: Text('sign_in'.tr,
                                    style: robotoMedium.copyWith(
                                        color: Theme.of(context).primaryColor)),
                              ),
                            ),
                          ]),
                    ),
                  ]),
            ),
          ]);
        }),
      ),
    );
  }

  void _register(AuthController authController, String countryCode) async {
    String phoneNumber = _phoneController.text.trim();

    // Validate phone number length first
    if (phoneNumber.length != 10) {
      showCustomSnackBar('phone_number_must_be_10_digits'.tr);
      return;
    }

    // Wait for any ongoing phone check to complete
    if (authController.isPhoneChecking) {
      showCustomSnackBar('please_wait_phone_verification'.tr);
      return;
    }

    // Check if phone exists in both systems (should prevent registration)
    PhoneCheckModel? phoneCheckResult = authController.phoneCheckResult;
    if (phoneCheckResult != null &&
        phoneCheckResult.isPhoneExistsInBothSystems) {
      showCustomSnackBar('your_phone_number_already_exists'.tr);
      return;
    }

    // If phone check hasn't been performed yet, do it now and wait for result
    if (phoneCheckResult == null && phoneNumber.isNotEmpty) {
      String fullPhoneNumber = countryCode + phoneNumber;
      phoneCheckResult = await authController.checkPhone(fullPhoneNumber);

      if (phoneCheckResult != null &&
          phoneCheckResult.isPhoneExistsInBothSystems) {
        showCustomSnackBar('your_phone_number_already_exists'.tr);
        return;
      }
    }

    SignUpBodyModel? signUpModel = await _prepareSignUpBody(countryCode);

    if (signUpModel == null) {
      return;
    } else {
      authController.registration(signUpModel).then((status) async {
        _handleResponse(status, countryCode);
      });
    }
  }

  void _handleResponse(ResponseModel status, String countryCode) {
    String password = _passwordController.text.trim();
    String numberWithCountryCode = _phoneController.text.trim();
    String email = _emailController.text.trim();

    if (status.isSuccess) {
      if (ResponsiveHelper.isDesktop(context)) {
        Get.find<CartController>().getCartDataOnline();
      }
      // Always show OTP verification after successful registration
      if (status.authResponseModel != null) {
        List<int> encoded = utf8.encode(password);
        String data = base64Encode(encoded);

        // Always use the regular verification screen (not Firebase OTP) to ensure
        // it uses the /api/v1/auth/verify-phone endpoint
        if (ResponsiveHelper.isDesktop(context)) {
          Get.back();
          Get.dialog(VerificationScreen(
            number: numberWithCountryCode,
            email: null,
            token: status.message,
            fromSignUp: true,
            fromForgetPassword: false,
            loginType: CentralizeLoginType.manual.name,
            password: password,
          ));
        } else {
          Get.toNamed(RouteHelper.getVerificationRoute(
            numberWithCountryCode,
            null,
            status.message,
            RouteHelper.signUp,
            data,
            CentralizeLoginType.manual.name,
          ));
        }
      } else {
        // If no authResponseModel, proceed to normal flow
        Get.find<ProfileController>().getUserInfo();
        Get.find<LocationController>()
            .navigateToLocationScreen(RouteHelper.signUp);
        if (ResponsiveHelper.isDesktop(context)) {
          Get.back();
        }
      }
    } else {
      showCustomSnackBar(status.message);
    }
  }

  Future<SignUpBodyModel?> _prepareSignUpBody(String countryCode) async {
    String name = _nameController.text.trim();
    String email = _emailController.text.trim();
    String number = _phoneController.text.trim();
    String password = _passwordController.text.trim();
    String confirmPassword = _confirmPasswordController.text.trim();
    String referCode = _referCodeController.text.trim();

    String numberWithCountryCode = number;
    debugPrint('afsfa $numberWithCountryCode');
    // PhoneValid phoneValid =
    //     await CustomValidator.isPhoneValid(numberWithCountryCode);
    // numberWithCountryCode = phoneValid.phone;

    if (_formKeySignUp!.currentState!.validate()) {
      if (name.isEmpty) {
        showCustomSnackBar('please_enter_your_name'.tr);
      }
      // else if (email.isEmpty) {
      //   showCustomSnackBar('enter_email_address'.tr);
      // }
      // else if (!GetUtils.isEmail(email)) {
      //   showCustomSnackBar('enter_a_valid_email_address'.tr);
      // }
      else if (number.isEmpty) {
        showCustomSnackBar('enter_phone_number'.tr);
      } else if (number.length != 10) {
        showCustomSnackBar('phone_number_must_be_10_digits'.tr);
      }
      // else if (!numberWithCountryCode.isValid) {
      //   showCustomSnackBar('invalid_phone_number'.tr);
      // }
      else if (password.isEmpty) {
        showCustomSnackBar('enter_password'.tr);
      } else if (password.length < 8) {
        showCustomSnackBar('password_should_be_8_characters'.tr);
      } else if (password != confirmPassword) {
        showCustomSnackBar('confirm_password_does_not_matched'.tr);
      } else if (referCode.isNotEmpty && referCode.length != 10) {
        showCustomSnackBar('invalid_refer_code'.tr);
      } else {
        SignUpBodyModel signUpBody = SignUpBodyModel(
          name: name,
          email: email,
          phone: numberWithCountryCode,
          password: password,
          refCode: referCode,
        );
        return signUpBody;
      }
    }
    return null;
  }
}
